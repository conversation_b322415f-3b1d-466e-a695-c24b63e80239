<template>
  <el-dialog
    v-model="visible"
    width="900px"
    :height="height"
    :title="t('device.fileDownload.packageProgram')"
    :close-on-click-modal="true"
    @close="onClose"
  >
    <div class="table-box">
      <ProTable
        ref="proTable"
        :columns="columns"
        :request-auto="false"
        table-key="filePackageDialog"
        :data="tableData"
        highlight-current-row
        :pagination="false"
        @select-all="selectionChange"
        @select="handleSelect"
        :data-callback="dataCallback"
        row-key="path"
        style="flex: 1; height: 600px"
      >
        <template #tableHeader="scope">
          <div class="flex flex-wrap gap-4 items-center header">
            <el-text class="mx-1">{{ t("matrix.packageProgram.saveDir") }}：</el-text>
            <el-input v-model="saveDir" :placeholder="t('matrix.packageProgram.selectSaveDir')" style="width: 220px" readonly>
              <template #append>
                <el-button :icon="Folder" @click="selectSaveDir" />
              </template>
            </el-input>
            <el-button
              type="primary"
              @click="addFileAndFolder"
              plain
              :icon="DocumentAdd"
              :title="t('device.fileDownload.addDownloadFilesAndFolders')"
            />
            <el-button type="primary" :disabled="tableData.length === 0 || packaging" :icon="Download" @click="handlePackage">{{
              t("matrix.packageProgram.packageBtn")
            }}</el-button>
            <el-button type="primary" :icon="Folder" @click="handleLocateDir" :disabled="!saveDir" :title="t('matrix.packageProgram.locateDir')">{{
              t("matrix.packageProgram.locateDir")
            }}</el-button>
            <el-button type="success" :icon="Upload" @click="importFile">{{ t("matrix.common.import") }}</el-button>
            <el-button type="success" :icon="Download" @click="exportFile">{{ t("matrix.common.export") }}</el-button>
            <el-button type="danger" :disabled="scope.selectedList.length == 0" :icon="Delete" plain @click="batchDelete(scope.selectedList)">{{
              t("matrix.common.delete")
            }}</el-button>
            <el-button type="danger" :icon="Delete" plain @click="batchClear">{{ t("matrix.common.clear") }}</el-button>
          </div>
        </template>
        <template #operation="scope">
          <el-button type="primary" link :icon="Upload" @click="handleMoveUp(scope.row)">{{ t("matrix.common.moveUp") }}</el-button>
          <el-button type="primary" link :icon="Download" @click="handleMoveDown(scope.row)">{{ t("matrix.common.moveDown") }}</el-button>
          <el-button type="primary" link :icon="Delete" @click="deleteFile(scope.row)">{{ t("matrix.common.delete") }}</el-button>
        </template>
      </ProTable>
      <ProgressDialog ref="progressDialog" />
      <CustomFileSelector ref="customFileSelector" @confirm="handleCustomFileSelectorConfirm" />
    </div>
  </el-dialog>
</template>

<script setup lang="tsx">
import { ref, nextTick, toRaw, watch, onMounted, computed } from "vue";
import { useI18n } from "vue-i18n";
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps } from "@/components/ProTable/interface";
import { Delete, Download, Folder, Upload, DocumentAdd } from "@element-plus/icons-vue";
import ProgressDialog from "../dialog/ProgressDialog.vue";
import CustomFileSelector from "@/components/CustomFileSelector/index.vue";
import { osControlApi } from "@/api/modules/biz/os";
import { ElMessage, ElMessageBox } from "element-plus";
import { matrixApi } from "@/api/modules/biz/matrix/matrix";
import { formatDateTimeWithMs } from "@/utils";
import { MathUtils } from "@/utils/mathUtils";

const props = defineProps({
  modelValue: Boolean,
  height: {
    type: String,
    default: "700px"
  }
});
const emit = defineEmits(["update:modelValue", "addToDownload"]);
const { t } = useI18n();
const visible = computed({
  get: () => props.modelValue,
  set: v => emit("update:modelValue", v)
});
const proTable = ref();
const progressDialog = ref();
const customFileSelector = ref();
const tableData = ref<any[]>([]);
const saveDir = ref("");
const packaging = ref(false);
const packagePath = ref("");
const selectIds = ref<string[]>([]);

const SAVE_DIR_KEY = "file_package_dialog_saveDir";
const TABLE_DATA_KEY = "file_package_dialog_tableData";
const SELECT_IDS_KEY = "file_package_dialog_selectIds";

// 缓存管理工具函数
const saveToCache = (key: string, data: any) => {
  try {
    const cacheData = {
      data,
      timestamp: Date.now(),
      version: "1.0.0" // 版本号，用于缓存兼容性检查
    };
    localStorage.setItem(key, JSON.stringify(cacheData));
  } catch (error) {
    console.warn(`缓存保存失败: ${key}`, error);
  }
};

const loadFromCache = (key: string) => {
  try {
    const cached = localStorage.getItem(key);
    if (!cached) return null;

    const cacheData = JSON.parse(cached);

    // 检查缓存版本兼容性
    if (cacheData.version !== "1.0.0") {
      console.log(`缓存版本不匹配，清除: ${key}`);
      localStorage.removeItem(key);
      return null;
    }

    // 检查缓存是否过期（24小时）
    const maxAge = 24 * 60 * 60 * 1000;
    if (Date.now() - cacheData.timestamp > maxAge) {
      console.log(`缓存已过期，清除: ${key}`);
      localStorage.removeItem(key);
      return null;
    }

    return cacheData.data;
  } catch (error) {
    console.warn(`缓存读取失败: ${key}`, error);
    localStorage.removeItem(key);
    return null;
  }
};

const syncSelection = () => {
  nextTick(() => {
    if (proTable.value && proTable.value.element) {
      tableData.value.forEach(row => {
        proTable.value.element.toggleRowSelection(row, selectIds.value.includes(row.path));
      });
    }
  });
};

onMounted(() => {
  // 使用新的缓存管理工具加载数据
  const savedDir = loadFromCache(SAVE_DIR_KEY);
  if (savedDir) saveDir.value = savedDir;

  const savedTable = loadFromCache(TABLE_DATA_KEY);
  if (savedTable && Array.isArray(savedTable)) {
    // 确保恢复的数据结构完整
    tableData.value = savedTable.map(item => ({
      ...item,
      // 确保必要字段存在
      fileSize: item.fileSize || 0,
      fileSizeAs: item.fileSizeAs || MathUtils.formatBytes(item.fileSize || 0),
      lastModified: item.lastModified || formatDateTimeWithMs(new Date()),
      modifiedAt: item.modifiedAt ? new Date(item.modifiedAt) : new Date(),
      createdAt: item.createdAt ? new Date(item.createdAt) : new Date()
    }));
  }

  const savedSelectIds = loadFromCache(SELECT_IDS_KEY);
  if (savedSelectIds && Array.isArray(savedSelectIds)) {
    selectIds.value = savedSelectIds;
  }

  nextTick(() => {
    syncSelection(); // 用统一方法初始化勾选
  });
});

watch(saveDir, val => {
  if (val) {
    saveToCache(SAVE_DIR_KEY, val);
  } else {
    localStorage.removeItem(SAVE_DIR_KEY);
  }
});

watch(
  tableData,
  val => {
    if (val && Array.isArray(val)) {
      // 使用新的缓存管理工具保存数据
      saveToCache(TABLE_DATA_KEY, val);
    } else {
      localStorage.removeItem(TABLE_DATA_KEY);
    }
    syncSelection(); // 数据变化时同步勾选
  },
  { deep: true }
);

watch(
  selectIds,
  val => {
    if (val && Array.isArray(val)) {
      saveToCache(SELECT_IDS_KEY, val);
    } else {
      localStorage.removeItem(SELECT_IDS_KEY);
    }
    syncSelection(); // 勾选变化时同步勾选
  },
  { deep: true }
);

const columns = computed<ColumnProps<any>[]>(() => [
  { type: "selection", prop: "checked", fixed: "left", width: 70 },
  { type: "index", label: t("matrix.common.index"), fixed: "left", width: 70 },
  { prop: "fileName", label: t("matrix.packageProgram.fileName") },
  { prop: "fileSizeAs", label: t("matrix.packageProgram.fileSize") },
  { prop: "path", label: t("matrix.packageProgram.filePath") },
  { prop: "lastModified", label: t("matrix.packageProgram.lastModified") },
  { prop: "operation", label: t("matrix.common.operation"), fixed: "right", width: 280 }
]);

const selectionChange = isSelectAll => {
  const map = isSelectAll.map(item => item.path);
  tableData.value.forEach(row => {
    if (map.includes(row.path)) {
      selectIds.value = [...new Set([...selectIds.value, row.path])];
    } else {
      selectIds.value = selectIds.value.filter(path => path !== row.path);
    }
  });
};
const handleSelect = (selection, row) => {
  nextTick(() => {
    const isSelected = selection.some(item => item.path === row.path);
    if (isSelected) {
      selectIds.value = [...new Set([...selectIds.value, row.path])];
    } else {
      selectIds.value = selectIds.value.filter(name => name !== row.path);
    }
  });
};

const selectSaveDir = async () => {
  const res = await osControlApi.selectFolder();
  let dir = "";
  if (Array.isArray(res) && res.length > 0) {
    dir = res[0];
  } else if (typeof res === "string") {
    dir = res;
  } else if (res && typeof res.path === "string") {
    dir = res.path;
  }
  saveDir.value = dir;
  packagePath.value = "";
};

const handlePackage = async () => {
  if (!saveDir.value) {
    ElMessage.warning(t("matrix.packageProgram.saveDirEmpty"));
    return;
  }
  const rawTableData = toRaw(tableData.value);
  const fileList = rawTableData
    .filter(item => selectIds.value.includes(item.path))
    .map(item => {
      // 创建一个干净的对象副本，避免循环引用和不可序列化的属性
      return {
        path: item.path,
        fileName: item.fileName,
        fileSize: item.fileSize,
        fileSizeStr: item.fileSizeStr,
        fileType: item.fileType,
        createTime: item.createTime,
        modifyTime: item.modifyTime,
        index: item.index
      };
    });
  if (!fileList.length) {
    ElMessage.warning(t("matrix.packageProgram.noFileSelected"));
    return;
  }
  packaging.value = true;
  progressDialog.value.show();
  try {
    const result = await matrixApi.handlePackage({
      saveDir: saveDir.value,
      fileList
    });
    let zipPath = "";
    if (result && (typeof result === "string" || result.data)) {
      zipPath = typeof result === "string" ? result : result.data;
      packagePath.value = zipPath;
    }
    if ((result && result.code === 0) || zipPath) {
      ElMessageBox({
        message: t("matrix.packageProgram.packageSuccess") + (zipPath ? `\n${t("matrix.packageProgram.zipPath", { zipPath })}` : ""),
        title: t("matrix.packageProgram.tip"),
        type: "success",
        showCancelButton: true,
        confirmButtonText: t("matrix.packageProgram.confirmButton"),
        cancelButtonText: t("device.fileDownload.addToDownload"),
        callback: action => {
          if (action === "cancel" && zipPath) {
            emit("addToDownload", zipPath);
            visible.value = false;
          }
        }
      });
    } else {
      ElMessageBox.alert(t("matrix.packageProgram.packageFailed", { msg: result?.msg }), t("matrix.packageProgram.tip"), {
        confirmButtonText: t("matrix.packageProgram.confirmButton"),
        type: "error"
      });
    }
  } catch (error) {
    const errorMsg = error && typeof error === "object" && "message" in error ? error.message : String(error);
    ElMessageBox.alert(t("matrix.packageProgram.packageFailed", { msg: errorMsg }), t("matrix.packageProgram.tip"), {
      confirmButtonText: t("matrix.packageProgram.confirmButton"),
      type: "error"
    });
  } finally {
    packaging.value = false;
    progressDialog.value.hide();
  }
};

const handleLocateDir = async () => {
  if (!saveDir.value) {
    ElMessage.warning(t("matrix.packageProgram.saveDirEmpty"));
    return;
  }
  await osControlApi.openDirectory({ id: saveDir.value });
};

const deleteFile = row => {
  tableData.value = tableData.value.filter(item => item.path !== row.path);
};

const handleMoveUp = row => {
  const index = findIndexByPath(row.path);
  if (index <= 0) return;
  const newData = [...tableData.value];
  [newData[index - 1], newData[index]] = [newData[index], newData[index - 1]];
  tableData.value = newData;
};
const findIndexByPath = path => {
  return tableData.value.findIndex(item => item.path === path);
};
const handleMoveDown = row => {
  const index = findIndexByPath(row.path);
  if (index >= tableData.value.length - 1) return;
  const newData = [...tableData.value];
  [newData[index], newData[index + 1]] = [newData[index + 1], newData[index]];
  tableData.value = newData;
};

const batchDelete = async rows => {
  const fileMap = rows.map(row => row.path);
  nextTick(() => {
    rows.forEach(row => {
      proTable.value?.element?.toggleRowSelection(row, false);
    });
  });
  tableData.value = tableData.value.filter(item => !fileMap.includes(item.path));
};

const dataCallback = data => {
  return {
    list: data.list,
    total: data.total
  };
};

const exportFile = async () => {
  const defaultPath = t("matrix.packageProgram.defaultExportFileName");
  const selectPath = await osControlApi.openSaveFileDialogByParams({
    title: t("matrix.packageProgram.exportTitle"),
    defaultPath,
    filterList: [{ name: "xlsx", extensions: ["xlsx"] }]
  });
  if (!selectPath) {
    return;
  }
  const path = String(selectPath);
  progressDialog.value.show();
  try {
    const rawTableData = toRaw(tableData.value);
    // 创建干净的数据副本，避免循环引用和不可序列化的属性
    // 根据 FileItem 接口和后端导出服务的要求，确保包含所有必要字段
    const fileItems = rawTableData.map((item, index) => ({
      checked: item.checked || false,
      index: index + 1,
      id: item.id || 0,
      fileName: item.fileName,
      fileSize: item.fileSize || 0,
      fileSizeAs: item.fileSizeAs || "", // 后端导出服务使用此字段
      type: item.type || "",
      path: item.path,
      lastModified: item.lastModified || "", // 后端导出服务使用此字段
      status: item.status || "",
      percentType: item.percentType || "",
      percent: item.percent || 0,
      checkSum: item.checkSum || 0,
      hasTask: item.hasTask || false,
      taskid: item.taskid || "",
      fileParentPath: item.fileParentPath || ""
    }));
    const result = await matrixApi.exportDownloadList({
      path,
      fileItems
    });
    if (Number(result.code) === 0) {
      ElMessageBox.alert(t("matrix.packageProgram.exportSuccess", { path }), t("matrix.common.tips"), {
        confirmButtonText: t("matrix.common.confirm"),
        type: "success"
      });
    } else {
      ElMessageBox.alert(t("matrix.packageProgram.exportFailed"), t("matrix.common.tips"), {
        confirmButtonText: t("matrix.common.confirm"),
        type: "error"
      });
    }
  } catch (error) {
    ElMessageBox.alert(t("matrix.packageProgram.exportFailed"), t("matrix.common.tips"), {
      confirmButtonText: t("matrix.common.confirm"),
      type: "error"
    });
  } finally {
    progressDialog.value.hide();
  }
};

const importFile = async () => {
  const selectPath = await osControlApi.selectFileByParams({
    title: t("matrix.packageProgram.importTitle"),
    filterList: [{ name: "xlsx", extensions: ["xlsx"] }]
  });
  if (!selectPath.path) {
    return;
  }
  const path = String(selectPath.path);
  progressDialog.value.show();
  try {
    const response = await matrixApi.importDownloadList({ path });
    if (Number(response.code) === 0) {
      const resData = response.data;
      if (Array.isArray(resData)) {
        batchClear();
        resData.forEach(file => {
          const result = tableData.value.filter(item => item.path === file.path);
          if (result && result.length > 0) {
            ElMessage.error(t("matrix.packageProgram.fileExists", { path: file.path }));
          } else {
            const row = {
              checked: false,
              id: file.id || 0,
              fileName: file.fileName,
              fileSize: file.fileSize || 0, // 确保有文件大小
              fileSizeAs: file.fileSizeAs || MathUtils.formatBytes(file.fileSize || 0),
              type: file.type || "",
              path: file.path,
              lastModified: file.lastModified || formatDateTimeWithMs(new Date()),
              modifiedAt: file.modifiedAt ? new Date(file.modifiedAt) : new Date(), // 保存原始时间对象
              createdAt: file.createdAt ? new Date(file.createdAt) : new Date(),
              status: "",
              percentType: "",
              percent: 0,
              checkSum: file.checkSum || 0,
              hasTask: false,
              taskid: "",
              fileParentPath: file.fileParentPath || "",
              isDirectory: file.isDirectory || false,
              extension: file.extension || ""
            };
            tableData.value.push(row);
          }
        });
      }
      ElMessageBox.alert(t("matrix.packageProgram.importSuccess"), t("matrix.common.tips"), {
        confirmButtonText: t("matrix.common.confirm"),
        type: "success"
      });
    } else {
      ElMessageBox.alert(t("matrix.packageProgram.importFailed", { msg: response.msg }), t("matrix.common.tips"), {
        confirmButtonText: t("matrix.common.confirm"),
        type: "error"
      });
    }
  } catch (error) {
    ElMessageBox.alert(t("matrix.packageProgram.importFailed"), t("matrix.common.tips"), {
      confirmButtonText: t("matrix.common.confirm"),
      type: "error"
    });
  } finally {
    progressDialog.value.hide();
  }
};

const batchClear = () => {
  nextTick(() => {
    proTable.value?.element?.clearSelection();
  });
  tableData.value = [];
};

const handleCustomFileSelectorConfirm = items => {
  const convertedItems = Array.isArray(items) ? items : [items];
  const fileItems = convertedItems.map(item => ({
    // 基本信息
    path: item.path,
    fileName: item.name,

    // 文件大小信息 - 确保完整保存
    fileSize: item.size || 0,
    fileSizeAs: MathUtils.formatBytes(item.size || 0),

    // 时间信息 - 确保完整保存
    lastModified: formatDateTimeWithMs(item.modifiedAt || new Date()),
    modifiedAt: item.modifiedAt || new Date(), // 保存原始时间对象用于排序
    createdAt: item.createdAt || new Date(),

    // 其他必要字段，确保数据结构完整
    checked: false,
    id: 0,
    type: "",
    status: "",
    percentType: "",
    percent: 0,
    checkSum: 0,
    hasTask: false,
    taskid: "",
    fileParentPath: "",

    // 文件类型信息
    isDirectory: item.isDirectory || false,
    extension: item.extension || ""
  }));
  let added = 0;
  const newPaths: string[] = [];
  fileItems.forEach(fileItem => {
    if (!tableData.value.some(item => item.path === fileItem.path)) {
      tableData.value.push(fileItem);
      newPaths.push(fileItem.path);
      added++;
    }
  });
  if (newPaths.length > 0) {
    selectIds.value = [...new Set([...selectIds.value, ...newPaths])];
    nextTick(() => {
      if (proTable.value && proTable.value.element) {
        tableData.value.forEach(row => {
          if (newPaths.includes(row.path)) {
            proTable.value.element.toggleRowSelection(row, true);
          }
        });
      }
    });
  }
  if (added > 0) {
    ElMessage.success(t("matrix.packageProgram.addFileSuccess", { count: added }));
  } else {
    ElMessage.info(t("matrix.packageProgram.addFileNone"));
  }
};

const addFileAndFolder = () => {
  customFileSelector.value.open();
};

// 清理过期缓存的工具函数
const cleanupExpiredCache = () => {
  const keys = [SAVE_DIR_KEY, TABLE_DATA_KEY, SELECT_IDS_KEY];
  keys.forEach(key => {
    try {
      const cached = localStorage.getItem(key);
      if (cached) {
        const cacheData = JSON.parse(cached);
        const maxAge = 24 * 60 * 60 * 1000; // 24小时
        if (Date.now() - cacheData.timestamp > maxAge) {
          localStorage.removeItem(key);
          console.log(`清理过期缓存: ${key}`);
        }
      }
    } catch (error) {
      // 如果缓存数据格式不正确，直接删除
      localStorage.removeItem(key);
    }
  });
};

function onClose() {
  // 关闭时清理过期缓存
  cleanupExpiredCache();
}
</script>

<style lang="css" scoped>
.table-box {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
  height: 600px;
}
.header {
  margin-bottom: 8px;
}
</style>

<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :columns="columns"
      :request-auto="false"
      table-key="packageProgram"
      :data="tableData"
      highlight-current-row
      :pagination="false"
      @select-all="selectionChange"
      @select="handleSelect"
      :data-callback="dataCallback"
      row-key="path"
      style="flex: 1; min-height: 0"
    >
      <template #tableHeader="scope">
        <div class="flex flex-wrap gap-4 items-center header">
          <el-text class="mx-1">{{ t("matrix.packageProgram.saveDir") }}：</el-text>
          <el-input v-model="saveDir" :placeholder="t('matrix.packageProgram.selectSaveDir')" style="width: 220px" readonly>
            <template #append>
              <el-button :icon="Folder" @click="selectSaveDir" />
            </template>
          </el-input>
          <el-button type="primary" @click="addFileAndFolder" plain :icon="DocumentAdd" :title="t('device.fileDownload.addDownloadFilesAndFolders')">
          </el-button>
          <el-button type="primary" :disabled="tableData.length === 0 || packaging" :icon="Download" @click="handlePackage">
            {{ t("matrix.packageProgram.packageBtn") }}
          </el-button>
          <el-button type="primary" :icon="Folder" @click="handleLocateDir" :disabled="!saveDir" :title="t('matrix.packageProgram.locateDir')">
            {{ t("matrix.packageProgram.locateDir") }}
          </el-button>
          <el-button type="success" :icon="Upload" @click="importFile">{{ t("matrix.common.import") }}</el-button>
          <el-button type="success" :icon="Download" @click="exportFile">{{ t("matrix.common.export") }}</el-button>
          <el-button type="danger" :disabled="scope.selectedList.length == 0" :icon="Delete" plain @click="batchDelete(scope.selectedList)">
            {{ t("matrix.common.delete") }}
          </el-button>
          <el-button type="danger" :icon="Delete" plain @click="batchClear">
            {{ t("matrix.common.clear") }}
          </el-button>
        </div>
      </template>
      <template #operation="scope">
        <el-button type="primary" link :icon="Upload" @click="handleMoveUp(scope.row)">{{ t("matrix.common.moveUp") }}</el-button>
        <el-button type="primary" link :icon="Download" @click="handleMoveDown(scope.row)">{{ t("matrix.common.moveDown") }}</el-button>
        <el-button type="primary" link :icon="Delete" @click="deleteFile(scope.row)">{{ t("matrix.common.delete") }}</el-button>
      </template>
    </ProTable>
    <ProgressDialog ref="progressDialog" />
    <CustomFileSelector ref="customFileSelector" @confirm="handleCustomFileSelectorConfirm" />
    <Console scopeid="matrix" />
  </div>
</template>

<script setup lang="tsx">
import { ref, nextTick, toRaw, watch, onMounted, computed } from "vue";
import { useI18n } from "vue-i18n";
import ProTable from "@/components/ProTable/index.vue";
import { ColumnProps } from "@/components/ProTable/interface";
import { Delete, Download, Folder, Upload, DocumentAdd } from "@element-plus/icons-vue";
import ProgressDialog from "../../debug/device/dialog/ProgressDialog.vue";
import CustomFileSelector from "@/components/CustomFileSelector/index.vue";
import { osControlApi } from "@/api/modules/biz/os";
import { ElMessage, ElMessageBox } from "element-plus";
import { matrixApi } from "@/api/modules/biz/matrix";
import Console from "@/views/biz/debug/device/components/Console.vue";
import { useGlobalStore } from "@/stores/modules";
import { createScopeDebugStore } from "@/stores/modules/debug";
import { formatDateTimeWithMs } from "@/utils";
import { MathUtils } from "@/utils/mathUtils";

const { t } = useI18n();
const proTable = ref();
const progressDialog = ref();
const customFileSelector = ref();
const tableData = ref<any[]>([]);
const saveDir = ref("");
const packaging = ref(false);
const packagePath = ref("");
const selectIds = ref<string[]>([]);
const globalStore = useGlobalStore();
const { addConsole } = createScopeDebugStore("matrix")();

// localStorage key
const SAVE_DIR_KEY = "matrix_packageProgram_saveDir";
const TABLE_DATA_KEY = "matrix_packageProgram_tableData";
const SELECT_IDS_KEY = "matrix_packageProgram_selectIds";

// 缓存管理工具函数
const saveToCache = (key: string, data: any) => {
  try {
    const cacheData = {
      data,
      timestamp: Date.now(),
      version: "1.0.1" // 支持Date对象序列化
    };
    localStorage.setItem(
      key,
      JSON.stringify(cacheData, (key, value) => {
        // 自定义序列化，处理Date对象
        if (value instanceof Date) {
          return { __type: "Date", value: value.toISOString() };
        }
        return value;
      })
    );
  } catch (error) {
    console.warn(`缓存保存失败: ${key}`, error);
  }
};

const loadFromCache = (key: string) => {
  try {
    const cached = localStorage.getItem(key);
    if (!cached) return null;

    const cacheData = JSON.parse(cached, (key, value) => {
      // 自定义反序列化，恢复Date对象
      if (value && typeof value === "object" && value.__type === "Date") {
        return new Date(value.value);
      }
      return value;
    });

    // 检查缓存版本兼容性
    if (cacheData.version !== "1.0.1") {
      console.log(`缓存版本不匹配，清除: ${key}`);
      localStorage.removeItem(key);
      return null;
    }

    // 检查缓存是否过期（24小时）
    const maxAge = 24 * 60 * 60 * 1000;
    if (Date.now() - cacheData.timestamp > maxAge) {
      console.log(`缓存已过期，清除: ${key}`);
      localStorage.removeItem(key);
      return null;
    }

    return cacheData.data;
  } catch (error) {
    console.warn(`缓存读取失败: ${key}`, error);
    localStorage.removeItem(key);
    return null;
  }
};

onMounted(() => {
  globalStore.isConsole = true;

  // 使用新的缓存管理工具加载数据
  const savedDir = loadFromCache(SAVE_DIR_KEY);
  if (savedDir) saveDir.value = savedDir;

  const savedTable = loadFromCache(TABLE_DATA_KEY);
  if (savedTable && Array.isArray(savedTable)) {
    // 确保恢复的数据结构完整，处理时间字段
    tableData.value = savedTable.map(item => ({
      ...item,
      // 确保必要字段存在
      fileSize: item.fileSize || 0,
      fileSizeAs: item.fileSizeAs || MathUtils.formatBytes(item.fileSize || 0),
      // 时间字段处理 - 兼容不同的时间字段名称
      lastModified: item.modifyTime instanceof Date ? formatDateTimeWithMs(item.modifyTime) : item.lastModified || formatDateTimeWithMs(new Date()),
      modifyTime: item.modifyTime instanceof Date ? item.modifyTime : new Date(),
      createTime: item.createTime instanceof Date ? item.createTime : new Date()
    }));
  }

  const savedSelectIds = loadFromCache(SELECT_IDS_KEY);
  if (savedSelectIds && Array.isArray(savedSelectIds)) {
    selectIds.value = savedSelectIds;
  }

  // 自动恢复勾选状态
  nextTick(() => {
    if (proTable.value && selectIds.value && Array.isArray(selectIds.value)) {
      tableData.value.forEach(row => {
        proTable.value.element?.toggleRowSelection(row, selectIds.value.includes(row.path));
      });
    }
  });
});

watch(saveDir, val => {
  if (val) {
    saveToCache(SAVE_DIR_KEY, val);
  } else {
    localStorage.removeItem(SAVE_DIR_KEY);
  }
});

watch(
  tableData,
  val => {
    if (val && Array.isArray(val)) {
      // 使用新的缓存管理工具保存数据
      saveToCache(TABLE_DATA_KEY, val);
    } else {
      localStorage.removeItem(TABLE_DATA_KEY);
    }
  },
  { deep: true }
);

watch(
  selectIds,
  val => {
    if (val && Array.isArray(val)) {
      saveToCache(SELECT_IDS_KEY, val);
    } else {
      localStorage.removeItem(SELECT_IDS_KEY);
    }
  },
  { deep: true }
);

const columns = computed<ColumnProps<any>[]>(() => [
  { type: "selection", prop: "checked", fixed: "left", width: 70 },
  { type: "index", label: t("matrix.common.index"), fixed: "left", width: 70 },
  {
    prop: "fileName",
    label: t("matrix.packageProgram.fileName"),
    sortable: true
  },
  {
    prop: "fileSize",
    label: t("matrix.packageProgram.fileSize"),
    width: 120,
    sortable: true,
    sortMethod: (a: any, b: any) => {
      // 按原始字节数比较
      const sizeA = Number(a?.fileSize) || 0;
      const sizeB = Number(b?.fileSize) || 0;
      return sizeA - sizeB;
    },
    render: scope => {
      // 显示格式化后的文件大小，如果没有则显示 "--"
      const sizeDisplay = scope.row.fileSizeAs || (scope.row.fileSize ? MathUtils.formatBytes(scope.row.fileSize) : "--");
      return <span>{sizeDisplay}</span>;
    }
  },
  {
    prop: "path",
    label: t("matrix.packageProgram.filePath"),
    sortable: true
  },
  {
    prop: "lastModified",
    label: t("matrix.packageProgram.lastModified"),
    sortable: true,
    sortMethod: (a: any, b: any) => {
      // 按时间排序
      const timeA = new Date(a.lastModified).getTime() || 0;
      const timeB = new Date(b.lastModified).getTime() || 0;
      return timeA - timeB;
    },
    render: scope => {
      // 显示格式化后的时间，如果没有则显示 "--"
      const timeDisplay = scope.row.lastModified || "--";
      return <span>{timeDisplay}</span>;
    }
  },
  { prop: "operation", label: t("matrix.common.operation"), fixed: "right", width: 280 }
]);

const selectionChange = isSelectAll => {
  const map = isSelectAll.map(item => item.path);
  tableData.value.forEach(row => {
    if (map.includes(row.path)) {
      selectIds.value = [...new Set([...selectIds.value, row.path])];
    } else {
      selectIds.value = selectIds.value.filter(path => path !== row.path);
    }
  });
};
const handleSelect = (selection, row) => {
  nextTick(() => {
    const isSelected = selection.some(item => item.path === row.path);
    if (isSelected) {
      selectIds.value = [...new Set([...selectIds.value, row.path])];
    } else {
      selectIds.value = selectIds.value.filter(name => name !== row.path);
    }
  });
};

const selectSaveDir = async () => {
  const res: any = await osControlApi.selectFolder();
  let dir = "";
  if (Array.isArray(res) && res.length > 0) {
    dir = res[0];
  } else if (typeof res === "string") {
    dir = res;
  } else if (res && typeof res.path === "string") {
    dir = res.path;
  }
  saveDir.value = dir;
  packagePath.value = "";
  addConsole(t("matrix.packageProgram.selectDirSuccess", { dir }));
};

const handlePackage = async () => {
  if (!saveDir.value) {
    ElMessage.warning(t("matrix.packageProgram.saveDirEmpty"));
    addConsole(t("matrix.packageProgram.saveDirEmpty"));
    return;
  }
  // 校验已勾选文件
  const rawTableData = toRaw(tableData.value);
  const fileList = rawTableData
    .filter(item => selectIds.value.includes(item.path))
    .map(item => {
      // 创建一个干净的对象副本，避免循环引用和不可序列化的属性
      return {
        path: item.path,
        fileName: item.fileName,
        fileSize: item.fileSize,
        fileSizeStr: item.fileSizeStr,
        fileType: item.fileType,
        createTime: item.createTime,
        modifyTime: item.modifyTime,
        index: item.index
      };
    });
  if (!fileList.length) {
    ElMessage.warning(t("matrix.packageProgram.noFileSelected"));
    addConsole(t("matrix.packageProgram.noFileSelected"));
    return;
  }
  packaging.value = true;
  progressDialog.value.show();
  try {
    const result = await matrixApi.handlePackage({
      saveDir: saveDir.value,
      fileList
    });
    // 兼容后端返回ApiResponse结构
    let zipPath = "";
    if (result && (typeof result === "string" || result.data)) {
      zipPath = typeof result === "string" ? result : result.data;
      packagePath.value = zipPath;
    }
    if ((result && result.code === 0) || zipPath) {
      addConsole(t("matrix.packageProgram.packageSuccess") + (zipPath ? `\n${t("matrix.packageProgram.zipPath", { zipPath })}` : ""));
      ElMessageBox.alert(
        t("matrix.packageProgram.packageSuccess") + (zipPath ? `\n${t("matrix.packageProgram.zipPath", { zipPath })}` : ""),
        t("matrix.packageProgram.tip"),
        {
          confirmButtonText: t("matrix.packageProgram.confirmButton"),
          type: "success",
          showCancelButton: !!zipPath,
          cancelButtonText: t("matrix.packageProgram.openFileButton"),
          callback: async action => {
            if (action === "cancel" && zipPath) {
              await osControlApi.openDirectory({ id: zipPath });
            }
          }
        }
      );
    } else {
      addConsole(t("matrix.packageProgram.packageFailed", { msg: result?.msg }));
      ElMessageBox.alert(t("matrix.packageProgram.packageFailed", { msg: result?.msg }), t("matrix.packageProgram.tip"), {
        confirmButtonText: t("matrix.packageProgram.confirmButton"),
        type: "error"
      });
    }
  } catch (error: any) {
    addConsole(t("matrix.packageProgram.packageFailed", { msg: error?.message }));
    ElMessageBox.alert(t("matrix.packageProgram.packageFailed", { msg: error?.message }), t("matrix.packageProgram.tip"), {
      confirmButtonText: t("matrix.packageProgram.confirmButton"),
      type: "error"
    });
  } finally {
    packaging.value = false;
    progressDialog.value.hide();
  }
};

const handleLocateDir = async () => {
  if (!saveDir.value) {
    ElMessage.warning(t("matrix.packageProgram.saveDirEmpty"));
    addConsole(t("matrix.packageProgram.saveDirEmpty"));
    return;
  }
  await osControlApi.openDirectory({ id: saveDir.value });
  addConsole(t("matrix.packageProgram.locateDirSuccess", { dir: saveDir.value }));
};

const deleteFile = row => {
  tableData.value = tableData.value.filter(item => item.path !== row.path);
};

const handleMoveUp = (row: any) => {
  const index = findIndexByPath(row.path);
  if (index <= 0) return;
  const newData = [...tableData.value];
  [newData[index - 1], newData[index]] = [newData[index], newData[index - 1]];
  tableData.value = newData;
  addConsole(t("matrix.packageProgram.moveUpSuccess", { name: row.fileName }));
};
const findIndexByPath = (path: string) => {
  return tableData.value.findIndex(item => item.path === path);
};
const handleMoveDown = (row: any) => {
  const index = findIndexByPath(row.path);
  if (index >= tableData.value.length - 1) return;
  const newData = [...tableData.value];
  [newData[index], newData[index + 1]] = [newData[index + 1], newData[index]];
  tableData.value = newData;
  addConsole(t("matrix.packageProgram.moveDownSuccess", { name: row.fileName }));
};

const batchDelete = async (rows: any[]) => {
  const fileMap = rows.map(row => row.path);
  nextTick(() => {
    rows.forEach(row => {
      proTable.value?.element?.toggleRowSelection(row, false);
    });
  });
  tableData.value = tableData.value.filter(item => !fileMap.includes(item.path));
  addConsole(t("matrix.packageProgram.deleteSuccess", { count: fileMap.length }));
};

const dataCallback = (data: any) => {
  return {
    list: data.list,
    total: data.total
  };
};

const exportFile = async () => {
  const defaultPath = t("matrix.packageProgram.defaultExportFileName");
  const selectPath = await osControlApi.openSaveFileDialogByParams({
    title: t("matrix.packageProgram.exportTitle"),
    defaultPath,
    filterList: [{ name: "xlsx", extensions: ["xlsx"] }]
  });
  if (!selectPath) {
    return;
  }
  const path = String(selectPath);
  progressDialog.value.show();
  try {
    const rawTableData = toRaw(tableData.value);
    // 创建干净的数据副本，避免循环引用和不可序列化的属性
    // 根据 FileItem 接口和后端导出服务的要求，确保包含所有必要字段
    const fileItems = rawTableData.map((item, index) => ({
      checked: item.checked || false,
      index: index + 1,
      id: item.id || 0,
      fileName: item.fileName,
      fileSize: item.fileSize || 0,
      fileSizeAs: item.fileSizeAs || "", // 后端导出服务使用此字段
      type: item.type || "",
      path: item.path,
      lastModified: item.lastModified || "", // 后端导出服务使用此字段
      status: item.status || "",
      percentType: item.percentType || "",
      percent: item.percent || 0,
      checkSum: item.checkSum || 0,
      hasTask: item.hasTask || false,
      taskid: item.taskid || "",
      fileParentPath: item.fileParentPath || ""
    }));
    const result = await matrixApi.exportDownloadList({
      path,
      fileItems
    });
    if (Number(result.code) === 0) {
      addConsole(t("matrix.packageProgram.exportSuccess", { path }));
      ElMessageBox.alert(t("matrix.packageProgram.exportSuccess", { path }), t("matrix.common.tips"), {
        confirmButtonText: t("matrix.common.confirm"),
        type: "success"
      });
    } else {
      addConsole(t("matrix.packageProgram.exportFailed"));
      ElMessageBox.alert(t("matrix.packageProgram.exportFailed"), t("matrix.common.tips"), {
        confirmButtonText: t("matrix.common.confirm"),
        type: "error"
      });
    }
  } catch (error) {
    addConsole(t("matrix.packageProgram.exportFailed"));
    ElMessageBox.alert(t("matrix.packageProgram.exportFailed"), t("matrix.common.tips"), {
      confirmButtonText: t("matrix.common.confirm"),
      type: "error"
    });
  } finally {
    progressDialog.value.hide();
  }
};

const importFile = async () => {
  const selectPath = await osControlApi.selectFileByParams({
    title: t("matrix.packageProgram.importTitle"),
    filterList: [{ name: "xlsx", extensions: ["xlsx"] }]
  });
  if (!selectPath.path) {
    return;
  }
  const path = String(selectPath.path);
  progressDialog.value.show();
  try {
    const response = await matrixApi.importDownloadList({ path });
    if (Number(response.code) === 0) {
      const resData = response.data;
      if (Array.isArray(resData)) {
        batchClear();
        resData.forEach(file => {
          const result = tableData.value.filter(item => item.path === file.path);
          if (result && result.length > 0) {
            ElMessage.error(t("matrix.packageProgram.fileExists", { path: file.path }));
            addConsole(t("matrix.packageProgram.fileExists", { path: file.path }));
          } else {
            const row = {
              checked: false,
              id: file.id,
              fileName: file.fileName,
              fileSize: 0,
              fileSizeAs: file.fileSizeAs,
              type: "",
              path: file.path,
              lastModified: file.lastModified,
              status: "",
              percentType: "",
              percent: 0,
              checkSum: 0,
              hasTask: false,
              taskid: "",
              fileParentPath: ""
            };
            tableData.value.push(row);
          }
        });
      }
      addConsole(t("matrix.packageProgram.importSuccess"));
      ElMessageBox.alert(t("matrix.packageProgram.importSuccess"), t("matrix.common.tips"), {
        confirmButtonText: t("matrix.common.confirm"),
        type: "success"
      });
    } else {
      addConsole(t("matrix.packageProgram.importFailed", { msg: response.msg }));
      ElMessageBox.alert(t("matrix.packageProgram.importFailed", { msg: response.msg }), t("matrix.common.tips"), {
        confirmButtonText: t("matrix.common.confirm"),
        type: "error"
      });
    }
  } catch (error) {
    addConsole(t("matrix.packageProgram.importFailed"));
    ElMessageBox.alert(t("matrix.packageProgram.importFailed"), t("matrix.common.tips"), {
      confirmButtonText: t("matrix.common.confirm"),
      type: "error"
    });
  } finally {
    progressDialog.value.hide();
  }
};

const batchClear = () => {
  nextTick(() => {
    proTable.value?.element?.clearSelection();
  });
  tableData.value = [];
  addConsole(t("matrix.packageProgram.clearSuccess"));
};

const handleCustomFileSelectorConfirm = (items: any) => {
  const convertedItems = Array.isArray(items) ? items : [items];
  const fileItems = convertedItems.map(item => ({
    path: item.path,
    fileName: item.name,
    fileSize: item.size || 0,
    fileSizeAs: MathUtils.formatBytes(item.size || 0),
    lastModified: formatDateTimeWithMs(item.modifiedAt || new Date())
  }));
  let added = 0;
  const addedPaths: string[] = [];
  fileItems.forEach(fileItem => {
    if (!tableData.value.some(item => item.path === fileItem.path)) {
      tableData.value.push(fileItem);
      added++;
      addedPaths.push(fileItem.path);
    }
  });
  if (addedPaths.length > 0) {
    selectIds.value = [...new Set([...selectIds.value, ...addedPaths])];
    nextTick(() => {
      if (proTable.value && proTable.value.element) {
        tableData.value.forEach(row => {
          if (addedPaths.includes(row.path)) {
            proTable.value.element.toggleRowSelection(row, true);
          }
        });
      }
    });
  }
  if (added > 0) {
    addConsole(t("matrix.packageProgram.addFileSuccess", { count: added }));
  } else {
    addConsole(t("matrix.packageProgram.addFileNone"));
  }
};

const addFileAndFolder = () => {
  addConsole(t("matrix.packageProgram.addFileStart"));
  customFileSelector.value.open();
};
</script>

<style lang="css" scoped>
.table-box {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
}
.header {
  margin-bottom: 8px;
}
</style>
